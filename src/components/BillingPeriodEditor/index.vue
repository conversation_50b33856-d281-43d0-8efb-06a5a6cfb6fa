<template>
  <div class="billing-period-editor">
    <div v-for="(period, index) in localValue" :key="index" class="period-item">
      <el-row :gutter="10" type="flex" align="middle">
        <el-col :span="20">
          <el-date-picker
            v-model="period.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="(value) => handleDateRangeChange(value, index)"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4" style="text-align: center">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            circle
            @click="removePeriod(index)"
            :disabled="localValue.length <= 1"
          />
        </el-col>
      </el-row>
    </div>

    <el-button
      type="primary"
      icon="el-icon-plus"
      size="small"
      @click="addPeriod"
      style="margin-top: 10px"
    >
      添加账单周期
    </el-button>
  </div>
</template>

<script>
export default {
  name: "BillingPeriodEditor",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      localValue: [],
    };
  },
  watch: {
    value: {
      handler(newVal) {
        if (Array.isArray(newVal) && newVal.length > 0) {
          this.localValue = newVal.map((item) => {
            // 将开始和结束日期转换为日期范围数组
            const startDate = item.billingStartDate || "";
            const endDate = item.billingEndDate || "";
            const dateRange =
              startDate && endDate ? [startDate, endDate] : null;

            return {
              billingStartDate: startDate,
              billingEndDate: endDate,
              dateRange: dateRange,
            };
          });
        } else {
          this.localValue = [
            {
              billingStartDate: "",
              billingEndDate: "",
              dateRange: null,
            },
          ];
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    addPeriod() {
      this.localValue.push({
        billingStartDate: "",
        billingEndDate: "",
        dateRange: null,
      });
      this.handleChange();
    },

    removePeriod(index) {
      if (this.localValue.length > 1) {
        this.localValue.splice(index, 1);
        this.handleChange();
      }
    },

    handleDateRangeChange(value, index) {
      if (value && Array.isArray(value) && value.length === 2) {
        // 日期范围选择器返回数组 [startDate, endDate]
        this.localValue[index].billingStartDate = value[0];
        this.localValue[index].billingEndDate = value[1];
        this.localValue[index].dateRange = value;
      } else {
        // 清空选择
        this.localValue[index].billingStartDate = "";
        this.localValue[index].billingEndDate = "";
        this.localValue[index].dateRange = null;
      }
      this.handleChange();
    },

    handleChange() {
      // 只传递后端需要的数据格式（billingStartDate 和 billingEndDate）
      const outputValue = this.localValue.map((item) => ({
        billingStartDate: item.billingStartDate,
        billingEndDate: item.billingEndDate,
      }));
      this.$emit("input", outputValue);
    },
  },
};
</script>

<style scoped>
.billing-period-editor {
  width: 100%;
}

.period-item {
  margin-bottom: 10px;
}

.period-item:last-child {
  margin-bottom: 0;
}
</style>
