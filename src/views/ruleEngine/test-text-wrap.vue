<template>
  <div class="test-container">
    <h2>LogicFlow 节点文字换行测试</h2>
    <div class="test-controls">
      <el-button @click="addLongTextNode">添加长文本节点</el-button>
      <el-button @click="addChineseTextNode">添加中文长文本节点</el-button>
      <el-button @click="clearNodes">清空节点</el-button>
    </div>
    <div ref="container" class="flow-container"></div>
  </div>
</template>

<script>
import { getLogicFlowConfig } from './components/NodeFlow/config/logicFlowConfig.js';

export default {
  name: 'TestTextWrap',
  data() {
    return {
      lf: null,
      nodeCounter: 0,
    };
  },
  mounted() {
    this.initLogicFlow();
  },
  methods: {
    initLogicFlow() {
      this.lf = getLogicFlowConfig(this.$refs.container, this);
      
      // 添加一个开始节点
      const startNode = {
        type: 'start-node',
        x: 150,
        y: 100,
        properties: {
          text: 'start',
          saved: true,
        },
        text: {
          x: 150,
          y: 100,
          value: 'start',
        },
      };
      
      this.lf.render({
        nodes: [startNode],
        edges: [],
      });
    },
    
    addLongTextNode() {
      this.nodeCounter++;
      const longText = `这是一个很长的节点名称用来测试文字换行功能是否正常工作${this.nodeCounter}`;
      
      const node = {
        type: 'custom-node',
        x: 150 + this.nodeCounter * 200,
        y: 200,
        properties: {
          nodeType: 'SCRIPT',
          name: longText,
          icon: 'script',
          color: '#00b099',
          width: 150,
        },
        text: {
          x: 150 + this.nodeCounter * 200,
          y: 200,
          value: longText,
        },
      };
      
      this.lf.addNode(node);
    },
    
    addChineseTextNode() {
      this.nodeCounter++;
      const chineseText = `的书本在归档的协议工作流程节点${this.nodeCounter}`;
      
      const node = {
        type: 'sub-chain-node',
        x: 150 + this.nodeCounter * 200,
        y: 300,
        properties: {
          nodeType: 'SUB_CHAIN',
          name: chineseText,
          icon: 'subchain',
          color: '#00b099',
          width: 150,
        },
        text: {
          x: 150 + this.nodeCounter * 200,
          y: 300,
          value: chineseText,
        },
      };
      
      this.lf.addNode(node);
    },
    
    clearNodes() {
      this.nodeCounter = 0;
      const startNode = {
        type: 'start-node',
        x: 150,
        y: 100,
        properties: {
          text: 'start',
          saved: true,
        },
        text: {
          x: 150,
          y: 100,
          value: 'start',
        },
      };
      
      this.lf.render({
        nodes: [startNode],
        edges: [],
      });
    },
    
    // 必需的方法（从原始配置中复制）
    $confirm() {
      return Promise.resolve();
    },
    
    $message: {
      warning: (msg) => console.warn(msg),
      error: (msg) => console.error(msg),
      success: (msg) => console.log(msg),
    },
    
    getLatestExpression() {
      // 空实现
    },
    
    isNodeCountChanged() {
      return false;
    },
    
    $router: {
      go: () => {},
    },
  },
};
</script>

<style scoped>
.test-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.test-controls {
  margin-bottom: 20px;
}

.test-controls .el-button {
  margin-right: 10px;
}

.flow-container {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f9f9f9;
}
</style>
