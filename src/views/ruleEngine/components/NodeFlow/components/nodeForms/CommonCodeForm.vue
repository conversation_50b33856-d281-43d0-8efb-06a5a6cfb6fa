<template>
  <div>
    <el-form-item
      label="内置脚本"
      prop="isBuiltIn"
      :rules="
        formRequired
          ? [{ required: false, message: '请选择是否使用内置脚本' }]
          : []
      "
    >
      <el-switch v-model="formData.isBuiltIn" @change="handleBuiltInChange" />
    </el-form-item>

    <el-form-item
      label="脚本标识"
      prop="scriptName"
      :rules="
        formRequired ? [{ required: false, message: '请输入脚本标识' }] : []
      "
    >
      <template v-if="formData.isBuiltIn">
        <el-select
          v-model="formData.scriptName"
          placeholder="请选择脚本标识"
          @change="handleScriptNameChange"
        >
          <el-option
            v-for="item in dictList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </template>
      <template v-else>
        <el-input v-model="formData.scriptName" placeholder="请输入脚本标识" />
      </template>
    </el-form-item>

    <div class="form-section">
      <template v-if="!formData.isBuiltIn">
        <el-form-item
          label="脚本代码"
          prop="scriptCode"
          :rules="
            formRequired ? [{ required: false, message: '请输入脚本代码' }] : []
          "
        >
          <sm-code-editor
            v-model="formData.scriptCode"
            mode="java"
            :validate="false"
            :show-error="false"
            height="300px"
          />
          <div v-if="codeTips" class="tip-text">{{ codeTips }}</div>
        </el-form-item>
      </template>
    </div>

    <template
      v-if="formData.isBuiltIn && formData.scriptName === 'fastDataCmp'"
    >
      <el-form-item
        label="Service Code"
        prop="paramsValue.serviceCode"
        :rules="[{ required: true, message: '请输入Service Code' }]"
      >
        <el-input
          v-model="formData.paramsValue.serviceCode"
          placeholder="请输入Service Code"
        />
      </el-form-item>

      <el-form-item
        label="数据标识"
        prop="paramsValue.dataCode"
        :rules="[{ required: true, message: '请输入数据标识' }]"
      >
        <el-input
          v-model="formData.paramsValue.dataCode"
          placeholder="请输入数据标识"
        />
      </el-form-item>

      <el-form-item label="查询参数" prop="paramsValue.params">
        <div style="display: flex; gap: 4px;">
          <div style="flex: 1;">
            <el-table :data="paramsArray" size="small" border>
              <el-table-column label="Key">
                <template slot-scope="scope">
                  <el-input
                    :value="scope.row.key"
                    placeholder="请输入key"
                    @blur="(e) => handleKeyChange(e, scope.row)"
                    :class="{ 'has-error': scope.row.keyError }"
                  />
                  <span v-if="scope.row.keyError" class="error-text">{{
                    scope.row.keyError
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="Value">
                <template slot-scope="scope">
                  <el-input
                    :value="scope.row.value"
                    placeholder="请输入value"
                    @blur="(e) => handleValueChange(e, scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100px">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="removeParamRow(scope.row.id)"
                    icon="el-icon-delete"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div style="width: auto;">
            <el-button type="text" @click="addParamRow" icon="el-icon-plus"
              >添加参数</el-button
            >
          </div>
        </div>
      </el-form-item>

      <el-form-item label="序列化对象" prop="paramsValue.objectClass">
        <el-input
          v-model="formData.paramsValue.objectClass"
          placeholder="示例: com.bangdao.data.center.dal.mysql.entity.SaleSettleEntity"
        />
      </el-form-item>
    </template>

    <!-- dbCmp 数据库比较 -->
    <template v-if="formData.isBuiltIn && formData.scriptName === 'dbCmp'">
      <el-form-item
        label="数据库类型"
        prop="paramsValue.dbType"
        :rules="[{ required: true, message: '请选择数据库类型' }]"
      >
        <el-select
          v-model="formData.paramsValue.dbType"
          placeholder="请选择数据库类型"
        >
          <el-option label="MySQL" value="mysql" />
          <el-option label="CK" value="ck" />
        </el-select>
      </el-form-item>

      <el-form-item
        label="自定义SQL"
        prop="paramsValue.customSql"
        :rules="[{ required: true, message: '请输入SQL语句' }]"
      >
        <el-input
          v-model="formData.paramsValue.customSql"
          type="textarea"
          :rows="4"
          placeholder="请输入SQL语句"
        />
      </el-form-item>

      <el-form-item
        label="数据标识"
        prop="paramsValue.dataCode"
        :rules="[{ required: true, message: '请输入数据标识' }]"
      >
        <el-input
          v-model="formData.paramsValue.dataCode"
          placeholder="请输入数据标识"
        />
      </el-form-item>

      <el-form-item label="序列化对象" prop="paramsValue.objectClass">
        <el-input
          v-model="formData.paramsValue.objectClass"
          placeholder="示例: com.bangdao.data.center.dal.mysql.entity.SaleSettleEntity"
        />
      </el-form-item>
    </template>

    <!-- iteratorCmp 迭代器比较 -->
    <template
      v-if="formData.isBuiltIn && formData.scriptName === 'iteratorCmp'"
    >
      <el-form-item
        label="数据标识"
        prop="paramsValue.dataCode"
        :rules="[{ required: true, message: '请输入数据标识' }]"
      >
        <el-input
          v-model="formData.paramsValue.dataCode"
          placeholder="请输入数据标识"
        />
      </el-form-item>
    </template>

    <!-- booleanCmp 布尔比较 -->
    <template v-if="formData.isBuiltIn && formData.scriptName === 'booleanCmp'">
      <el-form-item
        label="表达式"
        prop="paramsValue.expression"
        :rules="[{ required: true, message: '请输入表达式' }]"
      >
        <el-input
          v-model="formData.paramsValue.expression"
          placeholder="请输入表达式"
        />
      </el-form-item>
    </template>

    <!-- calculateCmp 计算比较 -->
    <template
      v-if="formData.isBuiltIn && formData.scriptName === 'calculateCmp'"
    >
      <el-form-item
        label="结果集"
        prop="paramsValue.target"
        :rules="[{ required: true, message: '请输入结果集' }]"
      >
        <el-input
          v-model="formData.paramsValue.target"
          placeholder="请输入结果集"
        />
      </el-form-item>

      <el-form-item
        label="计算公式"
        prop="paramsValue.formulaList"
        :rules="[
          {
            required: true,
            message: '请添加计算公式',
            validator: validateTable,
          },
        ]"
      >
        <el-table :data="formData.paramsValue.formulaList" size="small" border>
          <el-table-column label="序号" width="80px">
            <template slot-scope="scope">{{ scope.row.orderNo + 1 }}</template>
          </el-table-column>

          <el-table-column label="公式">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.formula"
                placeholder="请输入公式"
                @change="() => validateFormula(scope.row)"
              />
            </template>
          </el-table-column>

          <el-table-column label="舍入模式">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.roundingMode"
                placeholder="请选择舍入模式"
                style="width: 100%"
                @change="() => validateFormula(scope.row)"
              >
                <el-option
                  v-for="item in roundingModes"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="截断位数">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.roundingDigits"
                :min="0"
                style="width: 100%"
                @change="() => validateFormula(scope.row)"
              />
            </template>
          </el-table-column>

          <el-table-column label="结果键名">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.dataKey"
                placeholder="请输入结果键名"
                @change="() => validateFormula(scope.row)"
              />
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150px">
            <template slot-scope="scope">
              <div style="display: flex; gap: 4px;">
                <el-button
                  type="text"
                  icon="el-icon-arrow-up"
                  :disabled="scope.$index === 0"
                  @click="moveFormula(scope.$index, 'up')"
                >
                  上移
                </el-button>
                <el-button
                  type="text"
                  icon="el-icon-arrow-down"
                  :disabled="
                    scope.$index === formData.paramsValue.formulaList.length - 1
                  "
                  @click="moveFormula(scope.$index, 'down')"
                >
                  下移
                </el-button>
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="removeFormula(scope.row.orderNo)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-top: 8px">
          <el-button type="text" icon="el-icon-plus" @click="addFormula"
            >添加公式</el-button
          >
        </div>
      </el-form-item>
    </template>

    <!-- 新增类型：dingTalkCmp 钉钉消息组件 -->
    <template
      v-if="formData.isBuiltIn && formData.scriptName === 'dingTalkCmp'"
    >
      <el-form-item
        label="节点参数"
        prop="paramsValue.target"
        :rules="[{ required: true, message: '请输入节点参数' }]"
      >
        <el-input
          v-model="formData.paramsValue.target"
          placeholder="请输入节点参数"
        />
      </el-form-item>
      <el-form-item
        label="钉钉接口地址"
        prop="paramsValue.dingTalkWebHookUrl"
        :rules="[{ required: true, message: '请输入钉钉接口地址' }]"
      >
        <el-input
          v-model="formData.paramsValue.dingTalkWebHookUrl"
          placeholder="请输入钉钉接口地址"
        />
      </el-form-item>
      <el-form-item
        label="钉钉消息发送"
        prop="paramsValue.dingTalkContext"
        :rules="[{ required: true, message: '请输入钉钉消息发送' }]"
      >
        <el-input
          v-model="formData.paramsValue.dingTalkContext"
          placeholder="请输入钉钉消息发送"
        />
      </el-form-item>
    </template>
  </div>
</template>

<script>
import SmCodeEditor from "@/components/CodeEditor/index.vue";

export default {
  name: "CommonCodeForm",
  components: {
    SmCodeEditor,
  },
  data() {
    return {
      paramsArray: [],
      dictList: [],
      roundingModes: [
        { label: "四舍五入", value: "HALF_UP" },
        { label: "进位", value: "UP" },
        { label: "截断", value: "DOWN" },
      ],
    };
  },
  computed: {
    // 计算是否有参数错误
    hasParamsError() {
      return this.paramsArray.some(
        (item) => item.keyError || !item.key || !item.value
      );
    },
    // 计算有效的参数对象
    validParams() {
      const params = {};
      this.paramsArray.forEach((item) => {
        if (item.key && item.value) {
          params[item.key] = item.value;
        }
      });
      return Object.keys(params).length > 0 ? params : undefined;
    },
  },
  props: {
    formData: {
      type: Object,
      default: () => ({
        scriptCode: "",
        scriptName: "",
        isBuiltIn: false,
        paramsValue: {
          serviceCode: "",
          dataCode: "",
          params: {},
          objectClass: "",
          dbType: "mysql",
          customSql: "",
          expression: "",
          target: "",
          formulaList: [],
        },
      }),
    },
    codeTips: {
      type: String,
      default: "",
    },
    formRequired: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    handleBuiltInChange() {
      this.$set(this.formData, "scriptCode", undefined);
      this.$set(this.formData, "scriptName", undefined);
    },
    handleScriptNameChange(value) {
      // 定义不同脚本类型的默认参数值
      const defaultParams = {
        fastDataCmp: {
          serviceCode: "",
          dataCode: "",
          params: {},
          objectClass: "",
        },
        dbCmp: {
          dbType: "mysql",
          customSql: "",
          dataCode: "",
          objectClass: "",
        },
        iteratorCmp: {
          dataCode: "",
        },
        booleanCmp: {
          expression: "",
          dataCode: "",
        },
        calculateCmp: {
          target: "",
          formulaList: [],
        },
        dingTalkCmp: {
          target: "",
          dingTalkWebHookUrl: "",
          dingTalkContext: "",
        },
      };

      // 根据脚本类型重置表单数据
      if (defaultParams[value]) {
        const newFormData = {
          ...this.formData,
          paramsValue: defaultParams[value],
        };
        this.$emit("update:formData", newFormData);
        this.paramsArray = [];
      }
    },

    // 初始化参数数组
    initParamsArray(params = {}) {
      // 使用深拷贝确保数据独立性
      const paramsCopy = JSON.parse(JSON.stringify(params));
      this.paramsArray = Object.entries(paramsCopy).map(([key, value]) => ({
        id: Date.now(),
        key,
        value,
        keyError: "",
      }));
    },

    // 添加新参数行
    addParamRow() {
      this.paramsArray.push({
        id: Date.now(),
        key: "",
        value: "",
        keyError: "",
      });
    },

    // 删除参数行
    removeParamRow(id) {
      const index = this.paramsArray.findIndex((item) => item.id === id);
      if (index > -1) {
        this.paramsArray.splice(index, 1);
      }
    },

    // 验证并更新参数
    validateAndUpdateParams() {
      if (!this.hasParamsError) {
        const paramsCopy = JSON.parse(JSON.stringify(this.validParams || {}));
        const newFormData = {
          ...this.formData,
          paramsValue: {
            ...this.formData.paramsValue,
            params: paramsCopy,
          },
        };
        this.$nextTick(() => {
          this.$emit("update:formData", newFormData);
        });
      }
    },

    // 处理key值变更
    handleKeyChange(e, record) {
      if (!record) return;
      const value = e.target.value;
      this.$set(record, "key", value);
      const isDuplicate = this.paramsArray.some(
        (item) => item.id !== record.id && item.key === record.key
      );
      if (isDuplicate) {
        record.keyError = "key值不能重复";
      } else {
        record.keyError = "";
        // 立即更新params
        this.validateAndUpdateParams();
      }
    },

    // 处理value值变更
    handleValueChange(e, record) {
      if (!record) return;
      const value = e.target.value;
      this.$set(record, "value", value);
      // 立即更新params
      this.validateAndUpdateParams();
    },

    // 获取字典列表
    async getDictList() {
      try {
        const response = await this.getDicts("liteflow_chain_component");
        this.dictList = (response.data || []).map((item) => ({
          label: item.dictLabel,
          value: item.dictValue,
        }));
      } catch (error) {
        console.error("获取字典失败:", error);
        // 提供默认字典数据
        this.dictList = [
          // { label: "快速数据比较", value: "fastDataCmp" },
          // { label: "数据库比较", value: "dbCmp" },
          // { label: "迭代器比较", value: "iteratorCmp" },
          // { label: "布尔比较", value: "booleanCmp" },
          // { label: "计算比较", value: "calculateCmp" },
        ];
      }
    },
    // 添加新公式
    addFormula() {
      if (!this.formData.paramsValue.formulaList) {
        this.$set(this.formData.paramsValue, "formulaList", []);
      }

      const newFormula = {
        orderNo: this.formData.paramsValue.formulaList.length,
        formula: "",
        roundingMode: "HALF_UP",
        roundingDigits: 0,
        dataKey: "",
      };

      this.formData.paramsValue.formulaList.push(newFormula);
    },

    // 删除公式
    removeFormula(orderNo) {
      const index = this.formData.paramsValue.formulaList.findIndex(
        (item) => item.orderNo === orderNo
      );
      if (index > -1) {
        this.formData.paramsValue.formulaList.splice(index, 1);
        // 重新计算序号
        this.formData.paramsValue.formulaList.forEach((item, idx) => {
          item.orderNo = idx;
        });
      }
    },

    // 移动公式
    moveFormula(index, direction) {
      const list = this.formData.paramsValue.formulaList;
      if (direction === "up" && index > 0) {
        const temp = list[index];
        this.$set(list, index, list[index - 1]);
        this.$set(list, index - 1, temp);
        // 更新序号
        list.forEach((item, idx) => {
          item.orderNo = idx;
        });
      } else if (direction === "down" && index < list.length - 1) {
        const temp = list[index];
        this.$set(list, index, list[index + 1]);
        this.$set(list, index + 1, temp);
        // 更新序号
        list.forEach((item, idx) => {
          item.orderNo = idx;
        });
      }
    },

    // 验证公式
    validateFormula(record) {
      // 这里可以添加具体的验证逻辑
      if (!record.formula || !record.dataKey) {
        return false;
      }
      return true;
    },
    validateTable(_, value, callback) {
      // 检查表格数据是否为空
      if (!value || value.length === 0) {
        callback(new Error("请添加计算公式"));
        return;
      }

      // 检查每一行数据是否完整
      for (let i = 0; i < value.length; i++) {
        const row = value[i];
        // 检查公式
        if (!row.formula || !row.formula.trim()) {
          callback(new Error(`第${i + 1}行公式不能为空`));
          return;
        }
        // 检查舍入模式
        if (!row.roundingMode) {
          callback(new Error(`第${i + 1}行舍入模式不能为空`));
          return;
        }
        // 检查截断位数
        if (typeof row.roundingDigits !== "number" || row.roundingDigits < 0) {
          callback(new Error(`第${i + 1}行截断位数必须为非负数`));
          return;
        }
        // 检查结果键名
        if (!row.dataKey || !row.dataKey.trim()) {
          callback(new Error(`第${i + 1}行结果键名不能为空`));
          return;
        }
      }

      // 检查结果键名是否重复
      const dataKeys = value.map((item) => item.dataKey.trim());
      const uniqueDataKeys = new Set(dataKeys);
      if (dataKeys.length !== uniqueDataKeys.size) {
        callback(new Error("结果键名不能重复"));
        return;
      }

      callback();
    },
  },
  watch: {
    "formData.isBuiltIn": {
      immediate: true,
      handler(newVal) {
        if (newVal && this.formData.scriptCode) {
          const newFormData = {
            ...this.formData,
            scriptCode: "",
          };
          this.$emit("update:formData", newFormData);
        }
      },
    },
    "formData.paramsValue.params": {
      immediate: true,
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          // 使用深拷贝初始化参数数组
          this.initParamsArray(newVal);
        } else {
          this.paramsArray = [];
        }
      },
    },
  },
  mounted() {
    this.getDictList();
    // 确保初始化时设置正确的默认值
    if (this.formData.isBuiltIn && !this.formData.scriptName) {
      this.$set(this.formData, "scriptName", "");
      this.$set(this.formData, "paramsValue", {
        serviceCode: "",
        dataCode: "",
        params: {},
        objectClass: "",
        dbType: "mysql",
        customSql: "",
        expression: "",
        target: "",
        formulaList: [],
      });
    }
  },
};
</script>

<style lang="less" scoped>
.form-section {
  margin-bottom: 16px;
}

.has-error {
  border-color: #ff4d4f !important;
}

.error-text {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.tip-text {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}

.el-table {
  margin-bottom: 8px;
}

.el-button + .el-button {
  margin-left: 4px;
}
</style>
