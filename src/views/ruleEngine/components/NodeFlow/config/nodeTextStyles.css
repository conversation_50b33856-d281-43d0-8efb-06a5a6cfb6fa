/* LogicFlow 节点文字换行样式优化 */

/* 全局节点文字样式 */
.lf-node-text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 12px !important;
  line-height: 1.4 !important;
  color: #333 !important;
  text-anchor: start !important;
  dominant-baseline: text-before-edge;
}

/* 自定义节点文字样式 */
.lf-node[data-type="custom-node"] .lf-node-text {
  font-weight: 500;
  max-width: 100px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 子链节点文字样式 */
.lf-node[data-type="sub-chain-node"] .lf-node-text {
  font-weight: 500;
  max-width: 100px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 开始节点文字样式 */
.lf-node[data-type="start-node"] .lf-node-text {
  font-weight: 600;
  text-anchor: middle !important;
}

/* 功能节点文字样式 */
.lf-node[data-type="function-node"] .lf-node-text {
  font-weight: 500;
  text-anchor: middle !important;
  max-width: 80px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 文字换行容器 */
.lf-node-text tspan {
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}

/* 确保文字在节点内正确对齐 */
.lf-node-text-container {
  overflow: visible;
}

/* 修复文字位置偏移问题 */
.lf-node .lf-node-text {
  alignment-baseline: text-before-edge;
  dominant-baseline: text-before-edge;
}

/* 针对中文字符的优化 */
.lf-node-text[data-lang="zh"] {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  letter-spacing: 0.5px;
}

/* 长文本省略号样式（备用方案） */
.lf-node-text.ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* 节点悬停时的文字样式 */
.lf-node:hover .lf-node-text {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

/* 选中状态下的文字样式 */
.lf-node.lf-node-selected .lf-node-text {
  font-weight: 600;
}
