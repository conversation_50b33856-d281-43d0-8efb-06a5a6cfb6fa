import { h, RectNode, RectNodeModel } from '@logicflow/core';
import { nodeIcons } from './icons';

class CustomNodeModel extends RectNodeModel {
  initNodeData(data) {
    super.initNodeData(data);
    this.width = data.properties.width || 150;
    this.height = data.properties.height || 40;
    this.radius = 4;

    // 设置只有左右两个锚点
    const { width, height } = this;
    this.anchorsOffset = [
      [-width / 2, 0], // 左侧锚点
      [width / 2, 0], // 右侧锚点
    ];
  }

  getNodeStyle() {
    const style = super.getNodeStyle();
    const { properties } = this;

    if (properties.color) {
      style.stroke = properties.color;
    }

    return style;
  }

  getTextStyle() {
    const style = super.getTextStyle();
    const { x, y, width } = this;
    const textPaddingLeft = 36;

    return {
      ...style,
      fontSize: 14,
      color: '#333',
      fontWeight: 500,
      textAlign: 'left',
      lineHeight: 40,
      x: x - width / 2 + textPaddingLeft,
      y: y,
      textAnchor: 'start',
    };
  }

  getAnchorStyle() {
    const style = super.getAnchorStyle();
    style.hover.r = 8;
    style.hover.fill = '#fff';
    style.hover.stroke = style.stroke;
    return style;
  }

  getOutlineStyle() {
    const style = super.getOutlineStyle();
    style.stroke = '#00b099'; // 修改选中边框颜色
    style.hover.stroke = '#00b099';
    return style;
  }
}

class CustomNodeView extends RectNode {
  getShape() {
    const { model } = this.props;
    const { x, y, width, height, radius, properties } = model;
    const style = model.getNodeStyle();

    return h('g', {}, [
      // 主矩形
      h('rect', {
        ...style,
        x: x - width / 2,
        y: y - height / 2,
        width,
        height,
        rx: radius,
        ry: radius,
      }),
      // 图标
      h('path', {
        fill: style.stroke,
        d: nodeIcons[properties.icon],
        transform: `matrix(0.016,0,0,0.016,${x - width / 2 + 10},${y - 9})`,
        style: {
          pointerEvents: 'none',
          transition: 'all 0.3s ease',
        },
      }),
      // 装饰性边框
      h('rect', {
        ...style,
        x: x - width / 2,
        y: y - height / 2,
        width,
        height,
        rx: radius,
        ry: radius,
        strokeDasharray: '4, 2',
        strokeOpacity: 0.4,
        fillOpacity: 0,
      }),
    ]);
  }
}

export default {
  type: 'custom-node',
  view: CustomNodeView,
  model: CustomNodeModel,
};
