import { h, RectNode, RectNodeModel } from "@logicflow/core";
import { nodeIcons } from "./icons";

class CustomNodeModel extends RectNodeModel {
  initNodeData(data) {
    super.initNodeData(data);
    this.width = data.properties.width || 150;

    // 根据文字内容动态计算高度
    const textContent = data.text?.value || data.properties?.name || "";
    const estimatedLines = this.calculateTextLines(textContent, 100); // 100是textWidth
    this.height = Math.max(40, 20 + estimatedLines * 16); // 基础高度40，每行16px

    this.radius = 4;

    // 设置只有左右两个锚点
    const { width, height } = this;
    this.anchorsOffset = [
      [-width / 2, 0], // 左侧锚点
      [width / 2, 0], // 右侧锚点
    ];
  }

  // 计算文字行数的辅助方法
  calculateTextLines(text, maxWidth) {
    if (!text) return 1;

    // 简单的字符宽度估算（中文字符约12px，英文字符约6px）
    const getCharWidth = (char) => {
      return /[\u4e00-\u9fa5]/.test(char) ? 12 : 6;
    };

    let currentLineWidth = 0;
    let lines = 1;

    for (let i = 0; i < text.length; i++) {
      const charWidth = getCharWidth(text[i]);
      currentLineWidth += charWidth;

      if (currentLineWidth > maxWidth) {
        lines++;
        currentLineWidth = charWidth;
      }
    }

    return lines;
  }

  // 更新文字时重新计算节点高度
  updateText(value) {
    super.updateText(value);
    const estimatedLines = this.calculateTextLines(value, 100);
    this.height = Math.max(40, 20 + estimatedLines * 16);

    // 重新设置锚点位置
    const { width } = this;
    this.anchorsOffset = [
      [-width / 2, 0], // 左侧锚点
      [width / 2, 0], // 右侧锚点
    ];
  }

  getNodeStyle() {
    const style = super.getNodeStyle();
    const { properties } = this;

    if (properties.color) {
      style.stroke = properties.color;
    }

    return style;
  }

  getTextStyle() {
    const style = super.getTextStyle();
    const { x, y, width } = this;
    const textPaddingLeft = 36;

    return {
      ...style,
      fontSize: 12,
      color: "#333",
      fontWeight: 500,
      textAlign: "left",
      lineHeight: 1.4,
      x: x - width / 2 + textPaddingLeft,
      y: y,
      textAnchor: "start",
      textWidth: 100, // 设置文字宽度限制
      overflowMode: "autoWrap", // 启用自动换行
    };
  }

  getAnchorStyle() {
    const style = super.getAnchorStyle();
    style.hover.r = 8;
    style.hover.fill = "#fff";
    style.hover.stroke = style.stroke;
    return style;
  }

  getOutlineStyle() {
    const style = super.getOutlineStyle();
    style.stroke = "#00b099"; // 修改选中边框颜色
    style.hover.stroke = "#00b099";
    return style;
  }
}

class CustomNodeView extends RectNode {
  getShape() {
    const { model } = this.props;
    const { x, y, width, height, radius, properties } = model;
    const style = model.getNodeStyle();

    return h("g", {}, [
      // 主矩形
      h("rect", {
        ...style,
        x: x - width / 2,
        y: y - height / 2,
        width,
        height,
        rx: radius,
        ry: radius,
      }),
      // 图标
      h("path", {
        fill: style.stroke,
        d: nodeIcons[properties.icon],
        transform: `matrix(0.016,0,0,0.016,${x - width / 2 + 10},${y - 9})`,
        style: {
          pointerEvents: "none",
          transition: "all 0.3s ease",
        },
      }),
      // 装饰性边框
      h("rect", {
        ...style,
        x: x - width / 2,
        y: y - height / 2,
        width,
        height,
        rx: radius,
        ry: radius,
        strokeDasharray: "4, 2",
        strokeOpacity: 0.4,
        fillOpacity: 0,
      }),
    ]);
  }
}

export default {
  type: "custom-node",
  view: CustomNodeView,
  model: CustomNodeModel,
};
