window.sheetChart = {
	"name": "Chart",
	"color": "",
	"status": 0,
	"order": 8,
	"index": "Sheet_6az6nei65t1i_1596209937084",
	"celldata": [{
		"r": 0,
		"c": 0,
		"v": {
			"v": null,
			"m": "",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 0,
		"c": 1,
		"v": {
			"v": "Mon",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Mon",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 0,
		"c": 2,
		"v": {
			"v": "<PERSON><PERSON>",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Tues",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 0,
		"c": 3,
		"v": {
			"v": "Wed",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Wed",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 0,
		"c": 4,
		"v": {
			"v": "Thur",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Thur",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 0,
		"c": 5,
		"v": {
			"v": "Fri",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Fri",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 0,
		"c": 6,
		"v": {
			"v": "Sat",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Sat",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 0,
		"c": 7,
		"v": {
			"v": "Sun",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Sun",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 1,
		"c": 0,
		"v": {
			"v": "BUS",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "BUS",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 1,
		"c": 1,
		"v": {
			"v": 320,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "320",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 1,
		"c": 2,
		"v": {
			"v": 302,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "302",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 1,
		"c": 3,
		"v": {
			"v": 301,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "301",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 1,
		"c": 4,
		"v": {
			"v": 334,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "334",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 1,
		"c": 5,
		"v": {
			"v": 390,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "390",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 1,
		"c": 6,
		"v": {
			"v": 330,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "330",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 1,
		"c": 7,
		"v": {
			"v": 320,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "320",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 2,
		"c": 0,
		"v": {
			"v": "UBER",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "UBER",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 2,
		"c": 1,
		"v": {
			"v": 120,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "120",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 2,
		"c": 2,
		"v": {
			"v": 132,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "132",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 2,
		"c": 3,
		"v": {
			"v": 101,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "101",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 2,
		"c": 4,
		"v": {
			"v": 134,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "134",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 2,
		"c": 5,
		"v": {
			"v": 90,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "90",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 2,
		"c": 6,
		"v": {
			"v": 230,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "230",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 2,
		"c": 7,
		"v": {
			"v": 210,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "210",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 0,
		"v": {
			"v": "TAXI",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "TAXI",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 1,
		"v": {
			"v": 220,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "220",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 2,
		"v": {
			"v": 182,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "182",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 3,
		"v": {
			"v": 191,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "191",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 4,
		"v": {
			"v": 234,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "234",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 5,
		"v": {
			"v": 290,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "290",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 6,
		"v": {
			"v": 330,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "330",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 3,
		"c": 7,
		"v": {
			"v": 310,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "310",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 0,
		"v": {
			"v": "SUBWAY",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "SUBWAY",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 1,
		"v": {
			"v": 820,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "820",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 2,
		"v": {
			"v": 832,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "832",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 3,
		"v": {
			"v": 901,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "901",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 4,
		"v": {
			"v": 934,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "934",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 5,
		"v": {
			"v": 1290,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "1290",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 6,
		"v": {
			"v": 1330,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "1330",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 4,
		"c": 7,
		"v": {
			"v": 1320,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "1320",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 22,
		"c": 0,
		"v": {
			"v": "country",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "country",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 22,
		"c": 1,
		"v": {
			"v": "Population",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Population",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 23,
		"c": 0,
		"v": {
			"v": "India",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "India",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 23,
		"c": 1,
		"v": {
			"v": 1354051854,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "1354051854",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 24,
		"c": 0,
		"v": {
			"v": "Pakistan",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Pakistan",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 24,
		"c": 1,
		"v": {
			"v": 200813818,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "200813818",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 25,
		"c": 0,
		"v": {
			"v": "China",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "China",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 25,
		"c": 1,
		"v": {
			"v": 1415045928,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "1415045928",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 26,
		"c": 0,
		"v": {
			"v": "Japan",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Japan",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 26,
		"c": 1,
		"v": {
			"v": 127185332,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "127185332",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 27,
		"c": 0,
		"v": {
			"v": "South-Eastern",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "South-Eastern",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 27,
		"c": 1,
		"v": {
			"v": 655636576,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "655636576",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 28,
		"c": 0,
		"v": {
			"v": "Western",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Western",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 28,
		"c": 1,
		"v": {
			"v": 272298399,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "272298399",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 29,
		"c": 0,
		"v": {
			"v": "Eastern",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Eastern",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 29,
		"c": 1,
		"v": {
			"v": 433643132,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "433643132",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 30,
		"c": 0,
		"v": {
			"v": "Western",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Western",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 30,
		"c": 1,
		"v": {
			"v": 381980688,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "381980688",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 31,
		"c": 0,
		"v": {
			"v": "Northern",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Northern",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 31,
		"c": 1,
		"v": {
			"v": 237784677,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "237784677",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 32,
		"c": 0,
		"v": {
			"v": "Others",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Others",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 32,
		"c": 1,
		"v": {
			"v": 234512021,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "234512021",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 33,
		"c": 0,
		"v": {
			"v": "Europe",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"m": "Europe",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}, {
		"r": 33,
		"c": 1,
		"v": {
			"v": 742648010,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "742648010",
			"bg": null,
			"bl": 0,
			"it": 0,
			"ff": 0,
			"fs": 11,
			"fc": "rgb(51, 51, 51)",
			"ht": 1,
			"vt": 1
		}
	}],
	"row": 84,
	"column": 60,
	"config": {
		"merge": {},
		"rowlen": {
			"0": 20,
			"1": 20,
			"2": 20,
			"3": 20,
			"4": 20,
			"22": 20,
			"23": 20,
			"24": 20,
			"25": 20,
			"26": 20,
			"27": 20,
			"28": 20,
			"29": 20,
			"30": 20,
			"31": 20,
			"32": 20,
			"33": 20
		}
	},
	"pivotTable": null,
	"isPivotTable": false,
	"ch_width": 4560,
	"rh_height": 1807,
	"luckysheet_select_save": [{
		"left": 0,
		"width": 73,
		"top": 445,
		"height": 20,
		"left_move": 0,
		"width_move": 147,
		"top_move": 445,
		"height_move": 251,
		"row": [22, 33],
		"column": [0, 1],
		"row_focus": 22,
		"column_focus": 0
	}],
	"luckysheet_selection_range": [],
	"scrollLeft": 0,
	"scrollTop": 185,
	"chart": [{
		"chart_id": "chart_p145W6i73otw_1596209943446",
		"width": 400,
		"height": 250,
		"left": 20,
		"top": 120,
		"sheetIndex": "Sheet_6az6nei65t1i_1596209937084",
		"needRangeShow": true,
		"chartOptions": {
			"chart_id": "chart_p145W6i73otw_1596209943446",
			"chartAllType": "echarts|column|default",
			"chartPro": "echarts",
			"chartType": "pie",
			"chartStyle": "default",
			"chartData": [
				[{
					"v": null,
					"m": "",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Mon",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Mon",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Tues",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Tues",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Wed",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Wed",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Thur",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Thur",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Fri",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Fri",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Sat",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Sat",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Sun",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Sun",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "BUS",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "BUS",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 320,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "320",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 302,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "302",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 301,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "301",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 334,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "334",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 390,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "390",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 330,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "330",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 320,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "320",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "UBER",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "UBER",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 120,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "120",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 132,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "132",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 101,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "101",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 134,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "134",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 90,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "90",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 230,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "230",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 210,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "210",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "TAXI",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "TAXI",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 220,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "220",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 182,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "182",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 191,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "191",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 234,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "234",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 290,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "290",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 330,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "330",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 310,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "310",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "SUBWAY",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "SUBWAY",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 820,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "820",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 832,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "832",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 901,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "901",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 934,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "934",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 1290,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "1290",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 1330,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "1330",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 1320,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "1320",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}]
			],
			"rangeArray": [{
				"row": [0, 4],
				"column": [0, 7]
			}],
			"rangeTxt": "A1:H5",
			"rangeColCheck": {
				"exits": true,
				"range": [0, 0]
			},
			"rangeRowCheck": {
				"exits": true,
				"range": [0, 0]
			},
			"rangeConfigCheck": false,
			"rangeSplitArray": {
				"title": {
					"row": [0, 0],
					"column": [0, 0]
				},
				"rowtitle": {
					"row": [0, 0],
					"column": [1, 7]
				},
				"coltitle": {
					"row": [1, 4],
					"column": [0, 0]
				},
				"content": {
					"row": [1, 4],
					"column": [1, 7]
				},
				"type": "normal",
				"range": {
					"row": [0, 4],
					"column": [0, 7]
				}
			},
			"chartDataCache": {
				"label": ["Mon", "Tues", "Wed", "Thur", "Fri", "Sat", "Sun"],
				"xAxis": ["BUS", "UBER", "TAXI", "SUBWAY"],
				"series": [
					[320, 302, 301, 334, 390, 330, 320],
					[120, 132, 101, 134, 90, 230, 210],
					[220, 182, 191, 234, 290, 330, 310],
					[820, 832, 901, 934, 1290, 1330, 1320]
				],
				"series_tpye": {
					"0": "num",
					"1": "num",
					"2": "num",
					"3": "num",
					"4": "num",
					"5": "num",
					"6": "num"
				}
			},
			"chartDataSeriesOrder": {
				"0": 0,
				"1": 1,
				"2": 2,
				"3": 3,
				"4": 4,
				"5": 5,
				"6": 6,
				"length": 7
			},
			"defaultOption": {
				"title": {
					"show": false,
					"text": "默认标题",
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"position": {
						"value": "left-top",
						"offsetX": 40,
						"offsetY": 50
					}
				},
				"subtitle": {
					"show": false,
					"text": "",
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"distance": {
						"value": "auto",
						"cusGap": 40
					}
				},
				"config": {
					"color": "transparent",
					"fontFamily": "Sans-serif",
					"grid": {
						"value": "normal",
						"top": 5,
						"left": 10,
						"right": 20,
						"bottom": 10
					}
				},
				"legend": {
					"show": true,
					"selectMode": "multiple",
					"selected": [{
						"seriesName": "衣服",
						"isShow": true
					}, {
						"seriesName": "食材",
						"isShow": true
					}, {
						"seriesName": "图书",
						"isShow": true
					}],
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"position": {
						"value": "left-top",
						"offsetX": 40,
						"offsetY": 50,
						"direction": "horizontal"
					},
					"width": {
						"value": "auto",
						"cusSize": 25
					},
					"height": {
						"value": "auto",
						"cusSize": 14
					},
					"distance": {
						"value": "auto",
						"cusGap": 10
					},
					"itemGap": 10,
					"data": ["Mon", "Tues", "Wed", "Thur", "Fri", "Sat", "Sun"]
				},
				"tooltip": {
					"show": true,
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"backgroundColor": "rgba(50,50,50,0.7)",
					"triggerOn": "mousemove",
					"triggerType": "item",
					"axisPointer": {
						"type": "line",
						"style": {
							"color": "#555",
							"width": "normal",
							"type": "solid"
						}
					},
					"format": [{
						"seriesName": "衣服",
						"prefix": "",
						"suffix": "",
						"ratio": 1,
						"digit": "auto"
					}, {
						"seriesName": "食材",
						"prefix": "",
						"suffix": "",
						"ratio": 1,
						"digit": "auto"
					}, {
						"seriesName": "图书",
						"prefix": "",
						"suffix": "",
						"ratio": 1,
						"digit": "auto"
					}],
					"position": "auto"
				},
				"axis": {
					"axisType": "xAxisDown",
					"xAxisUp": {
						"show": false,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示X轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"prefix": "",
							"suffix": "",
							"optimize": 0,
							"distance": 0,
							"min": "auto",
							"max": "auto",
							"ratio": 1,
							"digit": "auto"
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						},
						"axisLine": {
							"onZero": false
						}
					},
					"xAxisDown": {
						"show": true,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示X轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"prefix": "",
							"suffix": "",
							"optimize": 0,
							"distance": 0,
							"min": null,
							"max": null,
							"ratio": 1,
							"digit": "auto"
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						},
						"data": ["BUS", "UBER", "TAXI", "SUBWAY"],
						"type": "category"
					},
					"yAxisLeft": {
						"show": true,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示Y轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"formatter": {
								"prefix": "",
								"suffix": "",
								"ratio": 1,
								"digit": "auto"
							},
							"split": 5,
							"min": null,
							"max": null,
							"prefix": "",
							"suffix": "",
							"ratio": 1,
							"digit": "auto",
							"distance": 0
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						},
						"type": "value"
					},
					"yAxisRight": {
						"show": false,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示Y轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"formatter": {
								"prefix": "",
								"suffix": "",
								"ratio": 1,
								"digit": "auto"
							},
							"split": 5,
							"min": null,
							"max": null,
							"prefix": "",
							"suffix": "",
							"ratio": 1,
							"digit": "auto",
							"distance": 0
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						}
					}
				},
				"series": [{
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [320, 120, 220, 820],
					"type": "bar",
					"name": "Mon",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [302, 132, 182, 832],
					"type": "bar",
					"name": "Tues",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [301, 101, 191, 901],
					"type": "bar",
					"name": "Wed",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [334, 134, 234, 934],
					"type": "bar",
					"name": "Thur",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [390, 90, 290, 1290],
					"type": "bar",
					"name": "Fri",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [330, 230, 330, 1330],
					"type": "bar",
					"name": "Sat",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [320, 210, 310, 1320],
					"type": "bar",
					"name": "Sun",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}],
				"seriesData": [
					[320, 120, 220, 820],
					[302, 132, 182, 832],
					[301, 101, 191, 901],
					[334, 134, 234, 934],
					[390, 90, 290, 1290],
					[330, 230, 330, 1330],
					[320, 210, 310, 1320]
				]
			}
		}
	}, {
		"chart_id": "chart_lpiiaae1543z_1596209948642",
		"width": 400,
		"height": 250,
		"left": 500,
		"top": 120,
		"sheetIndex": "Sheet_6az6nei65t1i_1596209937084",
		"needRangeShow": false,
		"chartOptions": {
			"chart_id": "chart_lpiiaae1543z_1596209948642",
			"chartAllType": "echarts|line|default",
			"chartPro": "echarts",
			"chartType": "pie",
			"chartStyle": "default",
			"chartData": [
				[{
					"v": null,
					"m": "",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Mon",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Mon",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Tues",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Tues",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Wed",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Wed",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Thur",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Thur",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Fri",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Fri",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Sat",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Sat",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Sun",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Sun",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "BUS",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "BUS",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 320,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "320",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 302,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "302",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 301,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "301",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 334,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "334",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 390,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "390",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 330,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "330",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 320,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "320",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "UBER",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "UBER",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 120,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "120",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 132,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "132",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 101,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "101",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 134,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "134",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 90,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "90",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 230,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "230",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 210,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "210",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "TAXI",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "TAXI",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 220,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "220",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 182,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "182",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 191,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "191",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 234,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "234",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 290,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "290",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 330,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "330",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 310,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "310",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "SUBWAY",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "SUBWAY",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 820,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "820",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 832,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "832",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 901,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "901",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 934,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "934",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 1290,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "1290",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 1330,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "1330",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 1320,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "1320",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}]
			],
			"rangeArray": [{
				"left": 0,
				"width": 73,
				"top": 0,
				"height": 20,
				"left_move": 0,
				"width_move": 591,
				"top_move": 0,
				"height_move": 104,
				"row": [0, 4],
				"column": [0, 7],
				"row_focus": 0,
				"column_focus": 0
			}],
			"rangeTxt": "A1:H5",
			"rangeColCheck": {
				"exits": true,
				"range": [0, 0]
			},
			"rangeRowCheck": {
				"exits": true,
				"range": [0, 0]
			},
			"rangeConfigCheck": false,
			"rangeSplitArray": {
				"title": {
					"row": [0, 0],
					"column": [0, 0]
				},
				"rowtitle": {
					"row": [0, 0],
					"column": [1, 7]
				},
				"coltitle": {
					"row": [1, 4],
					"column": [0, 0]
				},
				"content": {
					"row": [1, 4],
					"column": [1, 7]
				},
				"type": "normal",
				"range": {
					"left": 0,
					"width": 73,
					"top": 0,
					"height": 20,
					"left_move": 0,
					"width_move": 591,
					"top_move": 0,
					"height_move": 104,
					"row": [0, 4],
					"column": [0, 7],
					"row_focus": 0,
					"column_focus": 0
				}
			},
			"chartDataCache": {
				"label": ["Mon", "Tues", "Wed", "Thur", "Fri", "Sat", "Sun"],
				"xAxis": ["BUS", "UBER", "TAXI", "SUBWAY"],
				"series": [
					[320, 302, 301, 334, 390, 330, 320],
					[120, 132, 101, 134, 90, 230, 210],
					[220, 182, 191, 234, 290, 330, 310],
					[820, 832, 901, 934, 1290, 1330, 1320]
				],
				"series_tpye": {
					"0": "num",
					"1": "num",
					"2": "num",
					"3": "num",
					"4": "num",
					"5": "num",
					"6": "num"
				}
			},
			"chartDataSeriesOrder": {
				"0": 0,
				"1": 1,
				"2": 2,
				"3": 3,
				"4": 4,
				"5": 5,
				"6": 6,
				"length": 7
			},
			"defaultOption": {
				"title": {
					"show": false,
					"text": "默认标题",
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"position": {
						"value": "left-top",
						"offsetX": 40,
						"offsetY": 50
					}
				},
				"subtitle": {
					"show": false,
					"text": "",
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"distance": {
						"value": "auto",
						"cusGap": 40
					}
				},
				"config": {
					"color": "transparent",
					"fontFamily": "Sans-serif",
					"grid": {
						"value": "normal",
						"top": 5,
						"left": 10,
						"right": 20,
						"bottom": 10
					}
				},
				"legend": {
					"show": true,
					"selectMode": "multiple",
					"selected": [{
						"seriesName": "衣服",
						"isShow": true
					}, {
						"seriesName": "食材",
						"isShow": true
					}, {
						"seriesName": "图书",
						"isShow": true
					}],
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"position": {
						"value": "left-top",
						"offsetX": 40,
						"offsetY": 50,
						"direction": "horizontal"
					},
					"width": {
						"value": "auto",
						"cusSize": 25
					},
					"height": {
						"value": "auto",
						"cusSize": 14
					},
					"distance": {
						"value": "auto",
						"cusGap": 10
					},
					"itemGap": 10,
					"data": ["Mon", "Tues", "Wed", "Thur", "Fri", "Sat", "Sun"]
				},
				"tooltip": {
					"show": true,
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"backgroundColor": "rgba(50,50,50,0.7)",
					"triggerOn": "mousemove",
					"triggerType": "item",
					"axisPointer": {
						"type": "line",
						"style": {
							"color": "#555",
							"width": "normal",
							"type": "solid"
						}
					},
					"format": [{
						"seriesName": "衣服",
						"prefix": "",
						"suffix": "",
						"ratio": 1,
						"digit": "auto"
					}, {
						"seriesName": "食材",
						"prefix": "",
						"suffix": "",
						"ratio": 1,
						"digit": "auto"
					}, {
						"seriesName": "图书",
						"prefix": "",
						"suffix": "",
						"ratio": 1,
						"digit": "auto"
					}],
					"position": "auto"
				},
				"axis": {
					"axisType": "xAxisDown",
					"xAxisUp": {
						"show": false,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示X轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"prefix": "",
							"suffix": "",
							"optimize": 0,
							"distance": 0,
							"min": "auto",
							"max": "auto",
							"ratio": 1,
							"digit": "auto"
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						},
						"axisLine": {
							"onZero": false
						}
					},
					"xAxisDown": {
						"show": true,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示X轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"prefix": "",
							"suffix": "",
							"optimize": 0,
							"distance": 0,
							"min": null,
							"max": null,
							"ratio": 1,
							"digit": "auto"
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						},
						"data": ["BUS", "UBER", "TAXI", "SUBWAY"],
						"type": "category"
					},
					"yAxisLeft": {
						"show": true,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示Y轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"formatter": {
								"prefix": "",
								"suffix": "",
								"ratio": 1,
								"digit": "auto"
							},
							"split": 5,
							"min": null,
							"max": null,
							"prefix": "",
							"suffix": "",
							"ratio": 1,
							"digit": "auto",
							"distance": 0
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						},
						"type": "value"
					},
					"yAxisRight": {
						"show": false,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示Y轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"formatter": {
								"prefix": "",
								"suffix": "",
								"ratio": 1,
								"digit": "auto"
							},
							"split": 5,
							"min": null,
							"max": null,
							"prefix": "",
							"suffix": "",
							"ratio": 1,
							"digit": "auto",
							"distance": 0
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						}
					}
				},
				"series": [{
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [320, 120, 220, 820],
					"type": "line",
					"name": "Mon",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [302, 132, 182, 832],
					"type": "line",
					"name": "Tues",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [301, 101, 191, 901],
					"type": "line",
					"name": "Wed",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [334, 134, 234, 934],
					"type": "line",
					"name": "Thur",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [390, 90, 290, 1290],
					"type": "line",
					"name": "Fri",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [330, 230, 330, 1330],
					"type": "line",
					"name": "Sat",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}, {
					"itemStyle": {
						"color": null,
						"borderColor": "#000",
						"borderType": "solid",
						"borderWidth": 1
					},
					"lineStyle": {
						"color": null,
						"width": 1,
						"type": "solid"
					},
					"data": [320, 210, 310, 1320],
					"type": "line",
					"name": "Sun",
					"markPoint": {
						"data": []
					},
					"markLine": {
						"data": []
					},
					"markArea": {
						"data": []
					}
				}],
				"seriesData": [
					[320, 120, 220, 820],
					[302, 132, 182, 832],
					[301, 101, 191, 901],
					[334, 134, 234, 934],
					[390, 90, 290, 1290],
					[330, 230, 330, 1330],
					[320, 210, 310, 1320]
				]
			}
		}
	}, {
		"chart_id": "chart_ei765e0iKkoe_1596210011748",
		"width": 600,
		"height": 250,
		"left": 150,
		"top": 450,
		"sheetIndex": "Sheet_6az6nei65t1i_1596209937084",
		"needRangeShow": false,
		"chartOptions": {
			"chart_id": "chart_ei765e0iKkoe_1596210011748",
			"chartAllType": "echarts|pie|default",
			"chartPro": "echarts",
			"chartType": "pie",
			"chartStyle": "default",
			"chartData": [
				[{
					"v": "country",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "country",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": "Population",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Population",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "India",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "India",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 1354051854,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "1354051854",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "Pakistan",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Pakistan",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 200813818,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "200813818",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "China",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "China",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 1415045928,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "1415045928",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "Japan",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Japan",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 127185332,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "127185332",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "South-Eastern",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "South-Eastern",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 655636576,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "655636576",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "Western",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Western",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 272298399,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "272298399",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "Eastern",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Eastern",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 433643132,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "433643132",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "Western",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Western",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 381980688,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "381980688",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "Northern",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Northern",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 237784677,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "237784677",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "Others",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Others",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 234512021,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "234512021",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}],
				[{
					"v": "Europe",
					"ct": {
						"fa": "General",
						"t": "g"
					},
					"m": "Europe",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}, {
					"v": 742648010,
					"ct": {
						"fa": "General",
						"t": "n"
					},
					"m": "742648010",
					"bg": null,
					"bl": 0,
					"it": 0,
					"ff": 0,
					"fs": 11,
					"fc": "rgb(51, 51, 51)",
					"ht": 1,
					"vt": 1
				}]
			],
			"rangeArray": [{
				"left": 0,
				"width": 73,
				"top": 445,
				"height": 20,
				"left_move": 0,
				"width_move": 147,
				"top_move": 445,
				"height_move": 251,
				"row": [22, 33],
				"column": [0, 1],
				"row_focus": 22,
				"column_focus": 0
			}],
			"rangeTxt": "A23:B34",
			"rangeColCheck": {
				"exits": true,
				"range": [0, 0]
			},
			"rangeRowCheck": {
				"exits": true,
				"range": [0, 0]
			},
			"rangeConfigCheck": false,
			"rangeSplitArray": {
				"title": {
					"row": [0, 0],
					"column": [0, 0]
				},
				"rowtitle": {
					"row": [0, 0],
					"column": [1, 1]
				},
				"coltitle": {
					"row": [1, 11],
					"column": [0, 0]
				},
				"content": {
					"row": [1, 11],
					"column": [1, 1]
				},
				"type": "normal",
				"range": {
					"left": 0,
					"width": 73,
					"top": 445,
					"height": 20,
					"left_move": 0,
					"width_move": 147,
					"top_move": 445,
					"height_move": 251,
					"row": [22, 33],
					"column": [0, 1],
					"row_focus": 22,
					"column_focus": 0
				}
			},
			"chartDataCache": {
				"label": ["Population"],
				"xAxis": ["India", "Pakistan", "China", "Japan", "South-Eastern", "Western", "Eastern", "Western", "Northern", "Others", "Europe"],
				"series": [
					[1354051854],
					[200813818],
					[1415045928],
					[127185332],
					[655636576],
					[272298399],
					[433643132],
					[381980688],
					[237784677],
					[234512021],
					[742648010]
				],
				"series_tpye": {
					"0": "num"
				}
			},
			"chartDataSeriesOrder": {
				"0": 0,
				"length": 1
			},
			"defaultOption": {
				"title": {
					"show": false,
					"text": "默认标题",
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"position": {
						"value": "left-top",
						"offsetX": 40,
						"offsetY": 50
					}
				},
				"subtitle": {
					"show": false,
					"text": "",
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"distance": {
						"value": "auto",
						"cusGap": 40
					}
				},
				"config": {
					"color": "transparent",
					"fontFamily": "Sans-serif",
					"grid": {
						"value": "normal",
						"top": 5,
						"left": 10,
						"right": 20,
						"bottom": 10
					}
				},
				"legend": {
					"show": true,
					"selectMode": "multiple",
					"selected": [{
						"seriesName": "衣服",
						"isShow": true
					}, {
						"seriesName": "食材",
						"isShow": true
					}, {
						"seriesName": "图书",
						"isShow": true
					}],
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"position": {
						"value": "left-top",
						"offsetX": 40,
						"offsetY": 50,
						"direction": "horizontal"
					},
					"width": {
						"value": "auto",
						"cusSize": 25
					},
					"height": {
						"value": "auto",
						"cusSize": 14
					},
					"distance": {
						"value": "auto",
						"cusGap": 10
					},
					"itemGap": 10,
					"data": [{
						"name": "India",
						"textStyle": {
							"color": null
						},
						"value": 1354051854
					}, {
						"name": "Pakistan",
						"textStyle": {
							"color": null
						},
						"value": 200813818
					}, {
						"name": "China",
						"textStyle": {
							"color": null
						},
						"value": 1415045928
					}, {
						"name": "Japan",
						"textStyle": {
							"color": null
						},
						"value": 127185332
					}, {
						"name": "South-Eastern",
						"textStyle": {
							"color": null
						},
						"value": 655636576
					}, {
						"name": "Western",
						"textStyle": {
							"color": null
						},
						"value": 272298399
					}, {
						"name": "Eastern",
						"textStyle": {
							"color": null
						},
						"value": 433643132
					}, {
						"name": "Western",
						"textStyle": {
							"color": null
						},
						"value": 381980688
					}, {
						"name": "Northern",
						"textStyle": {
							"color": null
						},
						"value": 237784677
					}, {
						"name": "Others",
						"textStyle": {
							"color": null
						},
						"value": 234512021
					}, {
						"name": "Europe",
						"textStyle": {
							"color": null
						},
						"value": 742648010
					}]
				},
				"tooltip": {
					"show": true,
					"label": {
						"fontSize": 12,
						"color": "#333",
						"fontFamily": "sans-serif",
						"fontGroup": [],
						"cusFontSize": 12
					},
					"backgroundColor": "rgba(50,50,50,0.7)",
					"triggerOn": "mousemove",
					"triggerType": "item",
					"axisPointer": {
						"type": "line",
						"style": {
							"color": "#555",
							"width": "normal",
							"type": "solid"
						}
					},
					"format": [{
						"seriesName": "衣服",
						"prefix": "",
						"suffix": "",
						"ratio": 1,
						"digit": "auto"
					}, {
						"seriesName": "食材",
						"prefix": "",
						"suffix": "",
						"ratio": 1,
						"digit": "auto"
					}, {
						"seriesName": "图书",
						"prefix": "",
						"suffix": "",
						"ratio": 1,
						"digit": "auto"
					}],
					"position": "auto"
				},
				"axis": {
					"axisType": "xAxisDown",
					"xAxisUp": {
						"show": false,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示X轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"prefix": "",
							"suffix": "",
							"optimize": 0,
							"distance": 0,
							"min": "auto",
							"max": "auto",
							"ratio": 1,
							"digit": "auto"
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						},
						"axisLine": {
							"onZero": false
						}
					},
					"xAxisDown": {
						"show": true,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示X轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"prefix": "",
							"suffix": "",
							"optimize": 0,
							"distance": 0,
							"min": null,
							"max": null,
							"ratio": 1,
							"digit": "auto"
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						},
						"data": ["India", "Pakistan", "China", "Japan", "South-Eastern", "Western", "Eastern", "Western", "Northern", "Others", "Europe"],
						"type": "category"
					},
					"yAxisLeft": {
						"show": true,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示Y轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"formatter": {
								"prefix": "",
								"suffix": "",
								"ratio": 1,
								"digit": "auto"
							},
							"split": 5,
							"min": null,
							"max": null,
							"prefix": "",
							"suffix": "",
							"ratio": 1,
							"digit": "auto",
							"distance": 0
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						},
						"type": "value"
					},
					"yAxisRight": {
						"show": false,
						"title": {
							"showTitle": false,
							"text": "",
							"nameGap": 15,
							"rotate": 0,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"fzPosition": "end"
						},
						"name": "显示Y轴",
						"inverse": false,
						"tickLine": {
							"show": true,
							"width": 1,
							"color": "auto"
						},
						"tick": {
							"show": true,
							"position": "outside",
							"length": 5,
							"width": 1,
							"color": "auto"
						},
						"tickLabel": {
							"show": true,
							"label": {
								"fontSize": 12,
								"color": "#333",
								"fontFamily": "sans-serif",
								"fontGroup": [],
								"cusFontSize": 12
							},
							"rotate": 0,
							"formatter": {
								"prefix": "",
								"suffix": "",
								"ratio": 1,
								"digit": "auto"
							},
							"split": 5,
							"min": null,
							"max": null,
							"prefix": "",
							"suffix": "",
							"ratio": 1,
							"digit": "auto",
							"distance": 0
						},
						"netLine": {
							"show": false,
							"width": 1,
							"type": "solid",
							"color": "auto",
							"interval": {
								"value": "auto",
								"cusNumber": 0
							}
						},
						"netArea": {
							"show": false,
							"interval": {
								"value": "auto",
								"cusNumber": 0
							},
							"colorOne": "auto",
							"colorTwo": "auto"
						}
					}
				},
				"series": [{
					"name": "Population",
					"type": "pie",
					"radius": ["0%", "75%"],
					"data": [{
						"value": 1354051854,
						"name": "India",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}, {
						"value": 200813818,
						"name": "Pakistan",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}, {
						"value": 1415045928,
						"name": "China",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}, {
						"value": 127185332,
						"name": "Japan",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}, {
						"value": 655636576,
						"name": "South-Eastern",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}, {
						"value": 272298399,
						"name": "Western",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}, {
						"value": 433643132,
						"name": "Eastern",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}, {
						"value": 381980688,
						"name": "Western",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}, {
						"value": 237784677,
						"name": "Northern",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}, {
						"value": 234512021,
						"name": "Others",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}, {
						"value": 742648010,
						"name": "Europe",
						"label": {},
						"labelLine": {
							"lineStyle": {}
						},
						"itemStyle": {}
					}],
					"dataLabels": {},
					"seLabel": {},
					"seLine": {},
					"roseType": false
				}],
				"seriesData": [
					[1354051854, 200813818, 1415045928, 127185332, 655636576, 272298399, 433643132, 381980688, 237784677, 234512021, 742648010]
				]
			}
		}
	}]
}

// export default sheetChart